from braintrust import Eval
 
from autoevals import Levenshtein
 
bt_api_key='sk-in91pI0YSJ2ydhPAVvuJ16U33MlcE6gl0HxT5DI5tk2wsIoU'
BRAINTRUST_API_KEY = bt_api_key

Eval(
    "pedro-project1",  # Replace with your project name
    data=lambda: [
        {
            "input": "Foo",
            "expected": "Hi Foo",
        },
        {
            "input": "Bar",
            "expected": "Hello Bar",
        },
    ],  # Replace with your eval dataset
    task=lambda input: "Hi " + input,  # Replace with your LLM call
    scores=[Levenshtein],
)